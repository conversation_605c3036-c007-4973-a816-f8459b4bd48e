{"running": true, "start_time": "2025-05-24T22:09:20.830387", "pid": 80381, "error_count": 0, "last_error": null, "last_scan": "2025-05-24T22:31:28.736589", "synced_files": 25, "recent_events": [{"timestamp": "2025-05-24T22:12:29.577417", "path": "internal/validation/Augment Guidelines Integration Validation.md", "action": "modified", "status": "success", "checksum": "8df233ca4d326290885ac811c5a562bb6705e6d87fac4978bbb179f9da58906f", "error": null}, {"timestamp": "2025-05-24T22:12:25.276595", "path": "internal/validation/Augment Guidelines Integration Validation.md", "action": "modified", "status": "success", "checksum": "8df233ca4d326290885ac811c5a562bb6705e6d87fac4978bbb179f9da58906f", "error": null}, {"timestamp": "2025-05-24T22:12:25.093002", "path": "internal/validation/Augment Guidelines Integration Validation.md", "action": "modified", "status": "success", "checksum": "8df233ca4d326290885ac811c5a562bb6705e6d87fac4978bbb179f9da58906f", "error": null}, {"timestamp": "2025-05-24T22:12:24.887314", "path": "internal/validation/Augment Guidelines Integration Validation.md", "action": "modified", "status": "success", "checksum": "8df233ca4d326290885ac811c5a562bb6705e6d87fac4978bbb179f9da58906f", "error": null}]}